{"kind": "FETCH", "data": {"headers": {"access-control-allow-credentials": "true", "access-control-allow-headers": "Content-Type, Authorization, X-Requested-With", "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS", "access-control-allow-origin": "*", "access-control-max-age": "86400", "age": "0", "cache-control": "public, max-age=0, must-revalidate", "content-encoding": "br", "content-type": "application/json", "date": "Tue, 05 Aug 2025 03:20:03 GMT", "permissions-policy": "camera=(), microphone=(), geolocation=()", "referrer-policy": "origin-when-cross-origin", "server": "Vercel", "strict-transport-security": "max-age=63072000", "transfer-encoding": "chunked", "vary": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-matched-path": "/api/blog/[id]", "x-robots-tag": "noindex, nofollow", "x-vercel-cache": "MISS", "x-vercel-id": "bom1::iad1::n6hkm-1754364002133-d4f26d6bc7a9"}, "body": "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", "status": 200, "url": "https://cms.ashishkamat.com.np/api/blog/whats-new-in-nextjs-15-major-features-and-developer-upgrades-you-should-know-2025"}, "revalidate": 60, "tags": []}